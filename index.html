<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "//www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
 
<!-- basecode by octomoosey @ tumblr DO NOT DELETE -->
 
<html xmlns="//www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head><title>{Title}</title>
<link rel="shortcut icon" href="{Favicon}">
<link rel="alternate" type="application/rss+xml" href="{RSS}">
{block:Description}<meta name="description" content="{MetaDescription}" />{/block:Description}

<!--- ••• Colour Variables ••• --->

<meta name="color:background" content="#eaeaea"/>
<meta name="color:containerbg" content="#ffffff"/>
<meta name="color:borders" content="#bebebe"/>
<meta name="color:text" content="#898989"/>
<meta name="color:bold" content="#c30102"/>
<meta name="color:italic" content="#c30102"/>
<meta name="color:links" content="#c30102"/>
<meta name="color:linkshover" content="#bbb"/>
<meta name="color:quoteicon" content="#c30102"/>
<meta name="color:scrollbar" content="#ec380b"/>
<meta name="color:tooltipbg" content="#c30102">
<meta name="color:tooltiptext" content="#ffffff">
<meta name="color:creditbg" content="#ffffff">
<meta name="color:credit" content="#c30102"/>

<!--- ••• If Variables ••• --->
<meta name="if:400px" content="0"/>
<meta name="if:500px" content="1"/>
<meta name="if:hidetags" content="1"/>
<meta name="if:hidecaptions" content="1"/>

<!--- ••• Text Variables ••• --->
<meta name="text:fontsize" content="11"/>

<!--- ••• Links ••• --->
<meta name="text:link 1" content="link 1"/>
<meta name="text:link 1 url" content="/"/>
<meta name="text:link 2" content="link 2"/>
<meta name="text:link 2 url" content="/"/>
<meta name="text:link 3" content="link 3"/>
<meta name="text:link 3 url" content="/"/>

<!--- ••• Fonts ••• --->
<link rel="stylesheet" href="//cdn.linearicons.com/free/1.0.0/icon-font.min.css">

<style type="text/css">

iframe.tmblr-iframe.tmblr-iframe--desktop-loggedin-controls.iframe-controls--desktop, .tmblr-iframe {
    position:fixed;
    z-index:9!important;
    top:0px!important;
    right:-48px!important;
    white-space:nowrap;
    transform: scale(0.6,0.6);}    
   
#tumblr_lightbox {
    background-color:rgba(255, 255, 255, .9)!important;
    z-index:99999!important;}
 
#tumblr_lightbox img {opacity:0;}
 
#tumblr_lightbox_caption {
    font-weight:normal!important;
    text-shadow:none!important;}
 
#tumblr_lightbox_center_image, #tumblr_lightbox_left_image, #tumblr_lightbox_right_image {
    -moz-box-shadow:none!important;
    -webkit-box-shadow:none!important;
    box-shadow:none!important;
    -moz-border-radius:0px!important;
    -webkit-border-radius:0px!important;
    border-radius:0px!important;
    opacity:1!important;}
 
#tumblr_lightbox_left_image, #tumblr_lightbox_right_image {opacity:.4!important;}

::-webkit-scrollbar-thumb:vertical {
    border:3px solid {color:containerbg};
    height:10px; 
    background-color:{color:scrollbar};}
    
::-webkit-scrollbar {
    background-color:{color:scrollbar};
    height:7px;
    width:12px;
    border:5px solid {color:containerbg};}

/* ---••• General Styling •••--- */  

body {
    background:{color:background};
    color:{color:text};
    font-size:{text:fontsize}px;
    font-family:arial;
    font-weight:normal;
    position:relative;
    text-align:left;
    overflow:none;
    margin:0;
    top:0px;
    left:0px;
    line-height:150%;
    height:100%;
    width:100%;}
 
blockquote {
    text-align:justify;
    padding-left:10px;
    border-left:1px solid {color:borders};
    margin:10px 0px 10px 0px;}
    
.caption {
{block:IndexPage}    
    {block:Ifhidecaptions}display:none;{/block:Ifhidecaptions}  
{/block:IndexPage}}    
 
.caption blockquote {
    margin:10px 0px 10px 0px;}
   
blockquote img {
    max-width:100%;
    height:auto;}
   
blockquote iframe {
    max-width:100%;
    height:auto;}    
   
img {
    max-width:100%;
    height:auto;}
   
.caption img {
    max-width:100%; 
    height:auto;}
   
.posts img {
    max-width:100%;
    margin-top:5px;
    margin-bottom:5px;}
 
pre {
    font-family: 'Roboto', sans-serif;
    white-space: pre-wrap;      
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;    
    white-space: -o-pre-wrap;  
    word-wrap: break-word;}
 
a {
    text-decoration:none;
    font-size:calc({text:fontsize}px - 2px);
    text-transform:uppercase;
    color:{color:links};
    letter-spacing:1px;
    cursor:help;
     -webkit-transition: all 0.7s ease;
    transition: all 0.7s ease;
    -moz-transition: all 0.7s ease;
    -o-transition: all 0.7s ease;}
 
a:hover {
    cursor:help;
    letter-spacing:1px;
    color:{color:linkshover};}
 
p {
    margin:10px 0 10px 0;}
 
b, strong {
    font-size:calc({text:fontsize}px - 2px);
    text-transform:uppercase;
    color:{color:bold};}
 
i, em {
    color:{color:italic};}
 
small, big {
    font-size:1em;}
 
h1, h2, h3, h4 {
    font-size:26px;
    letter-spacing:2px;
    line-height:30px;
    text-align:center;}
    
ul {
    list-style: none;}

ul li::before {
    content: "\2022";  
    color: {color:links};
    font-weight: bold;
    display: inline-block; 
    width: 1em; 
    margin-left: -40px;}    
    
/* ---••• Post Styles •••--- */  

#container {
    background:{color:containerbg};
    border:25px solid {color:containerbg};
    height:500px;
    position:fixed;
    top:50%;
    margin-top:-275px;
    left:50%;
{block:If400px}
    width:650px;
    margin-left:-350px;
{/block:If400px}
{block:If500px}
    width:750px;
    margin-left:-400px;
{/block:If500px}     
    overflow-y:scroll;}

#entries {
    position:absolute;
    padding-top:50px;
    margin-left:215px;
{block:If400px}
    width:400px;
{/block:If400px}
{block:If500px}
    width:500px;
{/block:If500px}    
    margin-bottom:120px;}
    
.post { 
    position:relative;
    text-align:justify;
    display:block;
    margin-bottom:100px;
    color:{color:text};
{block:If400px}
    width:400px;
{/block:If400px}
{block:If500px}
    width:500px;
{/block:If500px}}

/* ---••• Post Info •••--- */  

.postinfo {
    font-size:8px;
    color:{color:text};}    

.postinfo a {
    margin-right:5px;
    font-size:8px;
    color:{color:text};}

.tags {
    font-size:8px;
{block:IndexPage}    
    {block:Ifhidetags}display:none;{/block:Ifhidetags}   
{/block:IndexPage}    
    margin-top:10px;}    

.tags a {
    font-size:8px;
    color:{color:text};
    padding:0px 5px 0px 0px;}
    
/* ---••• Post Notes •••--- */  

{block:PermalinkPage} 
ol.notes {
    font-size:11px;
    line-height:11px;
    margin:0px 0px 0px -40px;
    list-style-type:none;}
 
ol.notes li.note {
    padding:10px 0px;}
 
ol.notes li.note a {
    font-size:9px;
    text-transform:uppercase;}    
 
ol.notes li.note img.avatar {
    vertical-align:-5px;
    margin-right:10px;
    width:16px;
    height:16px;}
 
ol.notes li.note span.action {
    font-weight:none;}
 
ol.notes li.note .answer_content {
    font-weight:normal;}
 
ol.notes li.note blockquote {

    margin:10px 0px 0px 25px;}
 
ol.notes li.note blockquote a {
    text-decoration:none;}    
{/block:PermalinkPage}     

/* ---••• Post Types •••--- */
 
.posttitle {
    color:{color:text};
    font-size:calc({text:fontsize} + 2px);
    margin-bottom:5px;
    text-transform:uppercase;
    letter-spacing:2px;}    
 
/* ---••• Ask Posts •••--- */

.question {
    margin-bottom:10px;}
    
.ques {
    text-align:justify;}    

.asker {
    margin-bottom:5px;
    font-size:{text:fontsize}px;
    text-align:left;
    text-transform:uppercase;
    letter-spacing:2px;}

.asker a {
    padding-bottom:2px;
    color:{color:text};
    border-bottom:1px solid {color:text};
    font-size:{text:fontsize}px;
    text-align:center;
    text-transform:uppercase;
    letter-spacing:2px;}    
 
/* ---••• Quote Posts •••--- */
 
.titlequote {
    font-size:calc({text:fontsize}px - 1px);
    letter-spacing:2px;
    text-transform:uppercase;
    text-align: left;}
 
.source {
    font-size:;
    text-align:left;
    margin-top:10px;}
 
.ql {
    position:relative;
    float:left;
    margin-right:5px;
    bottom:-11px;
    font-size: 60px;
    color:{color:quoteicon};}
 
/* ---••• Audio Player •••--- */    
 
.playwrap {
    margin-top:0px;
    position:relative;}
   
.playbutton {
    top:25px;
    position:relative;
    margin-left:23px;
    z-index:6;
    width:33px;
    height:30px;
    overflow:hidden;}
   
.albumart img {
    position:relative;
    margin-top:-28px;
    margin-left:0px
    height:80px;
    width:80px;
    overflow:hidden;}    
           
.trackinfo {
    margin-bottom:10px;
    color:{color:text};
    position:relative;
    margin-left:100px;
    height:80px;
    margin-top:-90px;
    margin-bottom:20px;}
 
.trackname {
    position:relative;
    padding-top:25px;
    text-align:left;
    font-size:10px;
    text-transform:uppercase;
    font-weight: bold;
    letter-spacing:2px;}  
 
.artist {
    text-align:left;
    font-size:10px;
    text-transform:uppercase;
    letter-spacing:4px;}
 
.album {
    text-align:left;
    font-style: italic;
    letter-spacing:4px;}  
    
.hidden { visibility: hidden; }
.unhidden { visibility: visible; } 
    
/* ---••• Other •••--- */  
   
#s-m-t-tooltip {
    border:2px solid {color:tooltiptext};
    max-width:300px;
    letter-spacing:1px;
    margin:15px;
    padding:5px 5px 5px 6px;
    border-radius:0px;
    font-weight:700;
    color:{color:tooltiptext};
    background:{color:tooltipbg};
    z-index:999999;
    font-size:9px;
    font-style:none;
    font-weight:lighter;
    text-transform:uppercase;}  
    
/* ---••• Sidebar •••--- */     

#sidebar {
    height:500px;
    position:fixed;
    margin-top:0px;
    margin-left:20px;
    width:150px;}

#sidebarinner {
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    text-align: justify;  
    -moz-text-align-last: center; 
    text-align-last: center;}

#portrait {
    text-align:center;}    

#portrait img {
    margin-bottom:10px;
    border:2px solid {color:borders};
    -webkit-border-radius: 96px;
    -moz-border-radius: 96px;
    border-radius: 96px;}
    
#nav {
    text-align:center;
    margin-top:10px;}

#nav a {
    margin:0px 5px;}    
    
/* ---••• Pagination •••--- */
 
#pagination {
    margin-top:10px;
    text-align:center;
    color:{color:pagination};}
 
#pagination a {
    letter-spacing:1px;
    font-size:calc({text:fontsize}px - 2px);
    color:{color:pagination};}
   
.currentp {
    display:inline;
    font-size:calc({text:fontsize}px + 2px);}    
 
.totalp {
    display:inline;
    font-size:calc({text:fontsize}px - 2px);} 

/* ---••• Credit •••--- */   
    
#credit {
    padding:6px 8px;
    position:fixed;
    right:20px;
    bottom:20px;
    background:{color:creditbg};
    color:{color:credit};
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;}

#credit a {
    font-size:11px;
    width:20px;
    text-align:center;
    color:{color:credit};}    
 
    
</style>

</head>

<!---••• Scripts Misc •••--->
 
<script src="//static.tumblr.com/yzs4yqx/lOsokx99u/smoothscroll.min.js"></script>
<script src="//ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js"></script>
 
<!---••• Scripts Tooltips •••--->
 
<script src="//static.tumblr.com/fiw4iub/4jdnu5ta4/jquery.style-my-tooltips.js"></script>
 
<script>
(function($){
$(document).ready(function(){
$("a[title]").style_my_tooltips({
tip_follows_cursor:true,
tip_delay_time:90,
tip_fade_speed:600,
attribute:"title"
});
});
})(jQuery);
</script>

<body>

<!---••• if you alter the theme and would like to include your own credit, feel free to change here.  if using the theme 'as is' or with minimal changes, then please leave the credit in tact, thank you •••--->
<div id="credit"><a href="//octomoosey.tumblr.com" title="theme by octomoosey"><span class="lnr lnr-code"></span></a></div>

<div id="container">

<div id="sidebar">
<div id="sidebarinner">
<div id="portrait"><img src="{PortraitURL-64}"></div>
{description}
<div id="nav">
<a href="/">home</a>
<a href="/ask">ask</a>
{block:iflink1}<a href="{text:link 1 url}">{text:link 1}</a>{/block:iflink1}
{block:iflink2}<a href="{text:link 2 url}">{text:link 2}</a>{/block:iflink2}
{block:iflink3}<a href="{text:link 3 url}">{text:link 3}</a>{/block:iflink3}
</div>
<div id="pagination">{block:Pagination}
{block:PreviousPage}<a href="{PreviousPage}">Prev </a>{/block:PreviousPage}
<div class="currentp">{CurrentPage}</div> / <div class="totalp">{TotalPages}</div>
{block:NextPage}<a href="{NextPage}"> Next</a>{/block:NextPage}  
{/block:Pagination}</div>
</div>    
</div>

<div id="entries">

{block:Posts}

<!-- {block:NoRebloggedFrom}
{block:RebloggedFrom}{ReblogParentName}{/block:RebloggedFrom}
{/block:NoRebloggedFrom} -->

<div class="post" id="{PostID}">
    
{block:Text}
{block:Title}<div class="posttitle">{Title}</div>{/block:Title}{Body}
{/block:Text}

{block:Photo}
<div class="media">{LinkOpenTag}<img src="{PhotoURL-HighRes}" width="100%">{LinkCloseTag}</div>
{/block:Photo}
 
{block:Photoset}
{block:If400px}{Photoset-400}{/block:If400px}
{block:If500px}{Photoset-500}{/block:If500px}
{/block:Photoset}
 
{block:Video}
{block:If400px}{Video-400}{/block:If400px}
{block:If500px}{Video-500}{/block:If500px}
{/block:Video}

{block:Quote}
<div class="ql">&ldquo;</div>
<div class="titlequote">{Quote}</div>
{block:Source}<div class="source">{Source}</div>{/block:Source}
{/block:Quote}
 
{block:Link}
<h1><a href="{URL}" {Target}>{Name}</a></h1>
{block:Description}{Description}{/block:Description}
{/block:Link}
 
{block:Chat}
<div class="text">
{block:Title}<h3>{Title}</h3>{/block:Title}
{block:Lines}<p>
{block:Label}<b>{Label}</b>{/block:Label}
{Line}</p>
{/block:Lines}
</div>
{/block:Chat}
 
{block:Audio}
{block:AudioPlayer}
<div class="playwrap">
<div class="playbutton">{AudioPlayerGrey}</div>
{block:AlbumArt}<div class="albumart"><img src="{AlbumArtURL}"></div>{/block:AlbumArt}
<div class="trackinfo">
<div class="trackname">
{block:TrackName}{TrackName}{/block:TrackName}
</div>
<div class="artist">
{block:Artist}{Artist}{/block:Artist}
</div>
<div class="album">
{block:Album}{Album}{/block:Album}
</div>
</div>
</div>
{/block:AudioPlayer}
{/block:Audio}

{block:Answer}
<div class="question"><div class="asker">{Asker} asked:</div><div class="ques">{Question}</div></div>{Answer}
{/block:Answer}

{block:Caption}<div class="caption">{Caption}</div>{/block:Caption}

{block:Date}
<div class="postinfo">
<a href="{Permalink}"><span class="lnr lnr-clock"></span> {DayOfMonthWithZero}&middot{MonthNumberWithZero}&middot{ShortYear}</a> {block:NoteCount}<a href="{Permalink}"><span class="lnr lnr-bookmark"></span> {NoteCount}n</a>{/block:NoteCount} <a href="{ReblogURL}"><span class="lnr lnr-redo"></span>  rblg</a>
</div>
{block:Date}

<div class="tags">
{block:HasTags}
{block:Tags}
<a href="{TagURL}"><span class="lnr lnr-tag"></span> {Tag}</a>
{/block:Tags}
{/block:HasTags}
</div>

{block:PermalinkPage}{block:PostNotes}{PostNotes}{/block:PostNotes}{/block:PermalinkPage}
</div> 

{/block:Posts}

{block:ContentSource}<!-- {SourceURL}
{block:SourceLogo}<img src="{BlackLogoURL}"width="{LogoWidth}" height="{LogoHeight}" alt="{SourceTitle}" />{/block:SourceLogo}
{block:NoSourceLogo}{SourceLink}{/block:NoSourceLogo} -->
{/block:ContentSource}
</div>
</div>
</div>
</div>

</body>

</html>